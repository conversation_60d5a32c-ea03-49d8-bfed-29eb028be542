/**
 * 日志清理脚本
 * 用于清理现有的日志文件和目录
 */

const fs = require('fs');
const path = require('path');

function cleanupLogs() {
  const logsDir = path.join(__dirname, '../logs');
  
  try {
    if (fs.existsSync(logsDir)) {
      console.log('正在清理日志目录...');
      
      // 读取目录中的所有文件
      const files = fs.readdirSync(logsDir);
      
      // 删除所有日志文件
      files.forEach(file => {
        const filePath = path.join(logsDir, file);
        try {
          fs.unlinkSync(filePath);
          console.log(`已删除日志文件: ${file}`);
        } catch (error) {
          console.error(`删除文件失败: ${file}`, error.message);
        }
      });
      
      // 删除空目录
      try {
        fs.rmdirSync(logsDir);
        console.log('已删除日志目录');
      } catch (error) {
        console.error('删除目录失败:', error.message);
      }
      
      console.log('✅ 日志清理完成');
    } else {
      console.log('日志目录不存在，无需清理');
    }
  } catch (error) {
    console.error('清理日志时发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupLogs();
}

module.exports = cleanupLogs;
